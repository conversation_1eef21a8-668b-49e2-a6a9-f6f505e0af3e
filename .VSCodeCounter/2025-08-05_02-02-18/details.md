# Details

Date : 2025-08-05 02:02:18

Directory /Users/<USER>/Documents/note/python/todo/magentic-ui-main/src/magentic_ui

Total : 142 files,  18633 codes, 4567 comments, 2993 blanks, all 26193 lines

[Summary](results.md) / Details / [Diff Summary](diff.md) / [Diff Details](diff-details.md)

## Files
| filename | language | code | comment | blank | total |
| :--- | :--- | ---: | ---: | ---: | ---: |
| [src/magentic\_ui/\_\_init\_\_.py](/src/magentic_ui/__init__.py) | Python | 30 | 0 | 3 | 33 |
| [src/magentic\_ui/\_cli.py](/src/magentic_ui/_cli.py) | Python | 738 | 38 | 61 | 837 |
| [src/magentic\_ui/\_docker.py](/src/magentic_ui/_docker.py) | Python | 58 | 0 | 20 | 78 |
| [src/magentic\_ui/agents/\_\_init\_\_.py](/src/magentic_ui/agents/__init__.py) | Python | 11 | 0 | 2 | 13 |
| [src/magentic\_ui/agents/\_coder.py](/src/magentic_ui/agents/_coder.py) | Python | 477 | 112 | 60 | 649 |
| [src/magentic\_ui/agents/\_user\_proxy.py](/src/magentic_ui/agents/_user_proxy.py) | Python | 8 | 0 | 1 | 9 |
| [src/magentic\_ui/agents/\_utils.py](/src/magentic_ui/agents/_utils.py) | Python | 33 | 3 | 8 | 44 |
| [src/magentic\_ui/agents/file\_surfer/\_\_init\_\_.py](/src/magentic_ui/agents/file_surfer/__init__.py) | Python | 2 | 0 | 2 | 4 |
| [src/magentic\_ui/agents/file\_surfer/\_browser\_code\_helpers.py](/src/magentic_ui/agents/file_surfer/_browser_code_helpers.py) | Python | 109 | 38 | 33 | 180 |
| [src/magentic\_ui/agents/file\_surfer/\_code\_markdown\_file\_browser.py](/src/magentic_ui/agents/file_surfer/_code_markdown_file_browser.py) | Python | 263 | 106 | 46 | 415 |
| [src/magentic\_ui/agents/file\_surfer/\_file\_surfer.py](/src/magentic_ui/agents/file_surfer/_file_surfer.py) | Python | 539 | 114 | 87 | 740 |
| [src/magentic\_ui/agents/file\_surfer/\_tool\_definitions.py](/src/magentic_ui/agents/file_surfer/_tool_definitions.py) | Python | 169 | 0 | 9 | 178 |
| [src/magentic\_ui/agents/mcp/\_\_init\_\_.py](/src/magentic_ui/agents/mcp/__init__.py) | Python | 2 | 0 | 2 | 4 |
| [src/magentic\_ui/agents/mcp/\_agent.py](/src/magentic_ui/agents/mcp/_agent.py) | Python | 70 | 13 | 13 | 96 |
| [src/magentic\_ui/agents/mcp/\_config.py](/src/magentic_ui/agents/mcp/_config.py) | Python | 8 | 0 | 5 | 13 |
| [src/magentic\_ui/agents/users/\_\_init\_\_.py](/src/magentic_ui/agents/users/__init__.py) | Python | 3 | 0 | 2 | 5 |
| [src/magentic\_ui/agents/users/\_dummy\_user\_proxy.py](/src/magentic_ui/agents/users/_dummy_user_proxy.py) | Python | 71 | 29 | 11 | 111 |
| [src/magentic\_ui/agents/users/\_metadata\_user\_proxy.py](/src/magentic_ui/agents/users/_metadata_user_proxy.py) | Python | 428 | 37 | 19 | 484 |
| [src/magentic\_ui/agents/web\_surfer/\_\_init\_\_.py](/src/magentic_ui/agents/web_surfer/__init__.py) | Python | 7 | 0 | 2 | 9 |
| [src/magentic\_ui/agents/web\_surfer/\_cua\_web\_surfer.py](/src/magentic_ui/agents/web_surfer/_cua_web_surfer.py) | Python | 224 | 17 | 19 | 260 |
| [src/magentic\_ui/agents/web\_surfer/\_events.py](/src/magentic_ui/agents/web_surfer/_events.py) | Python | 9 | 0 | 3 | 12 |
| [src/magentic\_ui/agents/web\_surfer/\_prompts.py](/src/magentic_ui/agents/web_surfer/_prompts.py) | Python | 171 | 0 | 10 | 181 |
| [src/magentic\_ui/agents/web\_surfer/\_set\_of\_mark.py](/src/magentic_ui/agents/web_surfer/_set_of_mark.py) | Python | 122 | 53 | 35 | 210 |
| [src/magentic\_ui/agents/web\_surfer/\_tool\_definitions.py](/src/magentic_ui/agents/web_surfer/_tool_definitions.py) | Python | 630 | 11 | 31 | 672 |
| [src/magentic\_ui/agents/web\_surfer/\_web\_surfer.py](/src/magentic_ui/agents/web_surfer/_web_surfer.py) | Python | 1,600 | 331 | 181 | 2,112 |
| [src/magentic\_ui/approval\_guard.py](/src/magentic_ui/approval_guard.py) | Python | 251 | 27 | 50 | 328 |
| [src/magentic\_ui/backend/README.md](/src/magentic_ui/backend/README.md) | Markdown | 1 | 0 | 0 | 1 |
| [src/magentic\_ui/backend/\_\_init\_\_.py](/src/magentic_ui/backend/__init__.py) | Python | 5 | 0 | 2 | 7 |
| [src/magentic\_ui/backend/cli.py](/src/magentic_ui/backend/cli.py) | Python | 225 | 75 | 38 | 338 |
| [src/magentic\_ui/backend/database/\_\_init\_\_.py](/src/magentic_ui/backend/database/__init__.py) | Python | 4 | 0 | 2 | 6 |
| [src/magentic\_ui/backend/database/db\_manager.py](/src/magentic_ui/backend/database/db_manager.py) | Python | 329 | 77 | 60 | 466 |
| [src/magentic\_ui/backend/database/schema\_manager.py](/src/magentic_ui/backend/database/schema_manager.py) | Python | 288 | 230 | 48 | 566 |
| [src/magentic\_ui/backend/datamodel/\_\_init\_\_.py](/src/magentic_ui/backend/datamodel/__init__.py) | Python | 47 | 0 | 3 | 50 |
| [src/magentic\_ui/backend/datamodel/db.py](/src/magentic_ui/backend/datamodel/db.py) | Python | 193 | 4 | 35 | 232 |
| [src/magentic\_ui/backend/datamodel/types.py](/src/magentic_ui/backend/datamodel/types.py) | Python | 73 | 5 | 29 | 107 |
| [src/magentic\_ui/backend/teammanager/\_\_init\_\_.py](/src/magentic_ui/backend/teammanager/__init__.py) | Python | 2 | 0 | 2 | 4 |
| [src/magentic\_ui/backend/teammanager/teammanager.py](/src/magentic_ui/backend/teammanager/teammanager.py) | Python | 372 | 46 | 47 | 465 |
| [src/magentic\_ui/backend/utils/\_\_init\_\_.py](/src/magentic_ui/backend/utils/__init__.py) | Python | 0 | 0 | 1 | 1 |
| [src/magentic\_ui/backend/utils/utils.py](/src/magentic_ui/backend/utils/utils.py) | Python | 219 | 72 | 31 | 322 |
| [src/magentic\_ui/backend/web/\_\_init\_\_.py](/src/magentic_ui/backend/web/__init__.py) | Python | 0 | 0 | 1 | 1 |
| [src/magentic\_ui/backend/web/app.py](/src/magentic_ui/backend/web/app.py) | Python | 149 | 25 | 39 | 213 |
| [src/magentic\_ui/backend/web/config.py](/src/magentic_ui/backend/web/config.py) | Python | 11 | 1 | 7 | 19 |
| [src/magentic\_ui/backend/web/deps.py](/src/magentic_ui/backend/web/deps.py) | Python | 93 | 18 | 35 | 146 |
| [src/magentic\_ui/backend/web/initialization.py](/src/magentic_ui/backend/web/initialization.py) | Python | 66 | 23 | 20 | 109 |
| [src/magentic\_ui/backend/web/managers/\_\_init\_\_.py](/src/magentic_ui/backend/web/managers/__init__.py) | Python | 2 | 0 | 2 | 4 |
| [src/magentic\_ui/backend/web/managers/connection.py](/src/magentic_ui/backend/web/managers/connection.py) | Python | 540 | 143 | 77 | 760 |
| [src/magentic\_ui/backend/web/routes/\_\_init\_\_.py](/src/magentic_ui/backend/web/routes/__init__.py) | Python | 0 | 0 | 1 | 1 |
| [src/magentic\_ui/backend/web/routes/plans.py](/src/magentic_ui/backend/web/routes/plans.py) | Python | 184 | 18 | 41 | 243 |
| [src/magentic\_ui/backend/web/routes/runs.py](/src/magentic_ui/backend/web/routes/runs.py) | Python | 115 | 18 | 32 | 165 |
| [src/magentic\_ui/backend/web/routes/sessions.py](/src/magentic_ui/backend/web/routes/sessions.py) | Python | 123 | 20 | 26 | 169 |
| [src/magentic\_ui/backend/web/routes/settingsroute.py](/src/magentic_ui/backend/web/routes/settingsroute.py) | Python | 53 | 6 | 17 | 76 |
| [src/magentic\_ui/backend/web/routes/teams.py](/src/magentic_ui/backend/web/routes/teams.py) | Python | 25 | 5 | 12 | 42 |
| [src/magentic\_ui/backend/web/routes/validation.py](/src/magentic_ui/backend/web/routes/validation.py) | Python | 140 | 16 | 27 | 183 |
| [src/magentic\_ui/backend/web/routes/ws.py](/src/magentic_ui/backend/web/routes/ws.py) | Python | 92 | 11 | 16 | 119 |
| [src/magentic\_ui/cli/\_\_init\_\_.py](/src/magentic_ui/cli/__init__.py) | Python | 7 | 1 | 3 | 11 |
| [src/magentic\_ui/cli/pretty\_console.py](/src/magentic_ui/cli/pretty_console.py) | Python | 524 | 132 | 149 | 805 |
| [src/magentic\_ui/eval/README.md](/src/magentic_ui/eval/README.md) | Markdown | 0 | 0 | 1 | 1 |
| [src/magentic\_ui/eval/\_\_init\_\_.py](/src/magentic_ui/eval/__init__.py) | Python | 0 | 0 | 1 | 1 |
| [src/magentic\_ui/eval/basesystem.py](/src/magentic_ui/eval/basesystem.py) | Python | 37 | 46 | 10 | 93 |
| [src/magentic\_ui/eval/benchmark.py](/src/magentic_ui/eval/benchmark.py) | Python | 116 | 54 | 27 | 197 |
| [src/magentic\_ui/eval/benchmarks/README.md](/src/magentic_ui/eval/benchmarks/README.md) | Markdown | 0 | 0 | 1 | 1 |
| [src/magentic\_ui/eval/benchmarks/\_\_init\_\_.py](/src/magentic_ui/eval/benchmarks/__init__.py) | Python | 18 | 0 | 2 | 20 |
| [src/magentic\_ui/eval/benchmarks/assistantbench/README.md](/src/magentic_ui/eval/benchmarks/assistantbench/README.md) | Markdown | 9 | 0 | 1 | 10 |
| [src/magentic\_ui/eval/benchmarks/assistantbench/\_\_init\_\_.py](/src/magentic_ui/eval/benchmarks/assistantbench/__init__.py) | Python | 0 | 0 | 1 | 1 |
| [src/magentic\_ui/eval/benchmarks/assistantbench/assistantbench.py](/src/magentic_ui/eval/benchmarks/assistantbench/assistantbench.py) | Python | 93 | 18 | 14 | 125 |
| [src/magentic\_ui/eval/benchmarks/assistantbench/evaluate\_utils/\_\_init\_\_.py](/src/magentic_ui/eval/benchmarks/assistantbench/evaluate_utils/__init__.py) | Python | 0 | 0 | 1 | 1 |
| [src/magentic\_ui/eval/benchmarks/assistantbench/evaluate\_utils/assistantbench\_evaluator.py](/src/magentic_ui/eval/benchmarks/assistantbench/evaluate_utils/assistantbench_evaluator.py) | Python | 111 | 13 | 21 | 145 |
| [src/magentic\_ui/eval/benchmarks/assistantbench/evaluate\_utils/evaluate\_dicts.py](/src/magentic_ui/eval/benchmarks/assistantbench/evaluate_utils/evaluate_dicts.py) | Python | 58 | 1 | 13 | 72 |
| [src/magentic\_ui/eval/benchmarks/assistantbench/evaluate\_utils/evaluate\_factory.py](/src/magentic_ui/eval/benchmarks/assistantbench/evaluate_utils/evaluate_factory.py) | Python | 21 | 1 | 9 | 31 |
| [src/magentic\_ui/eval/benchmarks/assistantbench/evaluate\_utils/evaluate\_numbers.py](/src/magentic_ui/eval/benchmarks/assistantbench/evaluate_utils/evaluate_numbers.py) | Python | 28 | 2 | 6 | 36 |
| [src/magentic\_ui/eval/benchmarks/assistantbench/evaluate\_utils/evaluate\_strings.py](/src/magentic_ui/eval/benchmarks/assistantbench/evaluate_utils/evaluate_strings.py) | Python | 123 | 21 | 36 | 180 |
| [src/magentic\_ui/eval/benchmarks/assistantbench/evaluate\_utils/readme.md](/src/magentic_ui/eval/benchmarks/assistantbench/evaluate_utils/readme.md) | Markdown | 1 | 0 | 1 | 2 |
| [src/magentic\_ui/eval/benchmarks/assistantbench/evaluate\_utils/utils.py](/src/magentic_ui/eval/benchmarks/assistantbench/evaluate_utils/utils.py) | Python | 17 | 4 | 4 | 25 |
| [src/magentic\_ui/eval/benchmarks/baseqa.py](/src/magentic_ui/eval/benchmarks/baseqa.py) | Python | 13 | 1 | 5 | 19 |
| [src/magentic\_ui/eval/benchmarks/bearcubs/README.md](/src/magentic_ui/eval/benchmarks/bearcubs/README.md) | Markdown | 9 | 0 | 2 | 11 |
| [src/magentic\_ui/eval/benchmarks/bearcubs/\_\_init\_\_.py](/src/magentic_ui/eval/benchmarks/bearcubs/__init__.py) | Python | 0 | 0 | 1 | 1 |
| [src/magentic\_ui/eval/benchmarks/bearcubs/bearcubs.py](/src/magentic_ui/eval/benchmarks/bearcubs/bearcubs.py) | Python | 69 | 24 | 19 | 112 |
| [src/magentic\_ui/eval/benchmarks/custom/README.md](/src/magentic_ui/eval/benchmarks/custom/README.md) | Markdown | 0 | 0 | 1 | 1 |
| [src/magentic\_ui/eval/benchmarks/custom/\_\_init\_\_.py](/src/magentic_ui/eval/benchmarks/custom/__init__.py) | Python | 0 | 0 | 1 | 1 |
| [src/magentic\_ui/eval/benchmarks/custom/custom.py](/src/magentic_ui/eval/benchmarks/custom/custom.py) | Python | 122 | 42 | 17 | 181 |
| [src/magentic\_ui/eval/benchmarks/gaia/README.md](/src/magentic_ui/eval/benchmarks/gaia/README.md) | Markdown | 9 | 0 | 1 | 10 |
| [src/magentic\_ui/eval/benchmarks/gaia/\_\_init\_\_.py](/src/magentic_ui/eval/benchmarks/gaia/__init__.py) | Python | 0 | 0 | 1 | 1 |
| [src/magentic\_ui/eval/benchmarks/gaia/gaia.py](/src/magentic_ui/eval/benchmarks/gaia/gaia.py) | Python | 150 | 21 | 20 | 191 |
| [src/magentic\_ui/eval/benchmarks/gpqa/README.md](/src/magentic_ui/eval/benchmarks/gpqa/README.md) | Markdown | 14 | 0 | 3 | 17 |
| [src/magentic\_ui/eval/benchmarks/gpqa/\_\_init\_\_.py](/src/magentic_ui/eval/benchmarks/gpqa/__init__.py) | Python | 0 | 0 | 1 | 1 |
| [src/magentic\_ui/eval/benchmarks/gpqa/gpqa.py](/src/magentic_ui/eval/benchmarks/gpqa/gpqa.py) | Python | 106 | 7 | 18 | 131 |
| [src/magentic\_ui/eval/benchmarks/simpleqa/README.md](/src/magentic_ui/eval/benchmarks/simpleqa/README.md) | Markdown | 16 | 0 | 5 | 21 |
| [src/magentic\_ui/eval/benchmarks/simpleqa/\_\_init\_\_.py](/src/magentic_ui/eval/benchmarks/simpleqa/__init__.py) | Python | 0 | 0 | 1 | 1 |
| [src/magentic\_ui/eval/benchmarks/simpleqa/prompts.py](/src/magentic_ui/eval/benchmarks/simpleqa/prompts.py) | Python | 80 | 1 | 1 | 82 |
| [src/magentic\_ui/eval/benchmarks/simpleqa/simpleqa.py](/src/magentic_ui/eval/benchmarks/simpleqa/simpleqa.py) | Python | 145 | 23 | 26 | 194 |
| [src/magentic\_ui/eval/benchmarks/webgames/README.md](/src/magentic_ui/eval/benchmarks/webgames/README.md) | Markdown | 9 | 0 | 2 | 11 |
| [src/magentic\_ui/eval/benchmarks/webgames/\_\_init\_\_.py](/src/magentic_ui/eval/benchmarks/webgames/__init__.py) | Python | 0 | 0 | 1 | 1 |
| [src/magentic\_ui/eval/benchmarks/webgames/webgames.py](/src/magentic_ui/eval/benchmarks/webgames/webgames.py) | Python | 87 | 37 | 18 | 142 |
| [src/magentic\_ui/eval/benchmarks/webvoyager/README.md](/src/magentic_ui/eval/benchmarks/webvoyager/README.md) | Markdown | 28 | 0 | 22 | 50 |
| [src/magentic\_ui/eval/benchmarks/webvoyager/\_\_init\_\_.py](/src/magentic_ui/eval/benchmarks/webvoyager/__init__.py) | Python | 0 | 0 | 1 | 1 |
| [src/magentic\_ui/eval/benchmarks/webvoyager/webvoyager.py](/src/magentic_ui/eval/benchmarks/webvoyager/webvoyager.py) | Python | 211 | 46 | 28 | 285 |
| [src/magentic\_ui/eval/core.py](/src/magentic_ui/eval/core.py) | Python | 454 | 184 | 66 | 704 |
| [src/magentic\_ui/eval/evaluators.py](/src/magentic_ui/eval/evaluators.py) | Python | 199 | 65 | 22 | 286 |
| [src/magentic\_ui/eval/models.py](/src/magentic_ui/eval/models.py) | Python | 90 | 10 | 54 | 154 |
| [src/magentic\_ui/eval/systems/\_\_init\_\_.py](/src/magentic_ui/eval/systems/__init__.py) | Python | 3 | 0 | 2 | 5 |
| [src/magentic\_ui/eval/systems/example.py](/src/magentic_ui/eval/systems/example.py) | Python | 14 | 4 | 5 | 23 |
| [src/magentic\_ui/eval/systems/llm\_system.py](/src/magentic_ui/eval/systems/llm_system.py) | Python | 53 | 21 | 13 | 87 |
| [src/magentic\_ui/eval/utils.py](/src/magentic_ui/eval/utils.py) | Python | 35 | 11 | 12 | 58 |
| [src/magentic\_ui/guarded\_action.py](/src/magentic_ui/guarded_action.py) | Python | 154 | 14 | 42 | 210 |
| [src/magentic\_ui/input\_func.py](/src/magentic_ui/input_func.py) | Python | 36 | 8 | 19 | 63 |
| [src/magentic\_ui/learning/\_\_init\_\_.py](/src/magentic_ui/learning/__init__.py) | Python | 2 | 0 | 2 | 4 |
| [src/magentic\_ui/learning/learner.py](/src/magentic_ui/learning/learner.py) | Python | 76 | 59 | 20 | 155 |
| [src/magentic\_ui/learning/memory\_provider.py](/src/magentic_ui/learning/memory_provider.py) | Python | 145 | 28 | 35 | 208 |
| [src/magentic\_ui/magentic\_ui\_config.py](/src/magentic_ui/magentic_ui_config.py) | Python | 64 | 44 | 12 | 120 |
| [src/magentic\_ui/task\_team.py](/src/magentic_ui/task_team.py) | Python | 221 | 14 | 23 | 258 |
| [src/magentic\_ui/teams/\_\_init\_\_.py](/src/magentic_ui/teams/__init__.py) | Python | 3 | 0 | 2 | 5 |
| [src/magentic\_ui/teams/orchestrator/\_\_init\_\_.py](/src/magentic_ui/teams/orchestrator/__init__.py) | Python | 2 | 0 | 2 | 4 |
| [src/magentic\_ui/teams/orchestrator/\_group\_chat.py](/src/magentic_ui/teams/orchestrator/_group_chat.py) | Python | 191 | 26 | 23 | 240 |
| [src/magentic\_ui/teams/orchestrator/\_orchestrator.py](/src/magentic_ui/teams/orchestrator/_orchestrator.py) | Python | 1,176 | 160 | 128 | 1,464 |
| [src/magentic\_ui/teams/orchestrator/\_prompts.py](/src/magentic_ui/teams/orchestrator/_prompts.py) | Python | 979 | 19 | 51 | 1,049 |
| [src/magentic\_ui/teams/orchestrator/\_sentinel\_prompts.py](/src/magentic_ui/teams/orchestrator/_sentinel_prompts.py) | Python | 44 | 1 | 4 | 49 |
| [src/magentic\_ui/teams/orchestrator/\_utils.py](/src/magentic_ui/teams/orchestrator/_utils.py) | Python | 34 | 4 | 5 | 43 |
| [src/magentic\_ui/teams/orchestrator/orchestrator\_config.py](/src/magentic_ui/teams/orchestrator/orchestrator_config.py) | Python | 21 | 22 | 4 | 47 |
| [src/magentic\_ui/teams/roundrobin\_orchestrator.py](/src/magentic_ui/teams/roundrobin_orchestrator.py) | Python | 340 | 43 | 47 | 430 |
| [src/magentic\_ui/tools/\_\_init\_\_.py](/src/magentic_ui/tools/__init__.py) | Python | 24 | 0 | 2 | 26 |
| [src/magentic\_ui/tools/bing\_search.py](/src/magentic_ui/tools/bing_search.py) | Python | 137 | 41 | 21 | 199 |
| [src/magentic\_ui/tools/mcp/\_\_init\_\_.py](/src/magentic_ui/tools/mcp/__init__.py) | Python | 8 | 0 | 2 | 10 |
| [src/magentic\_ui/tools/mcp/\_aggregate\_workbench.py](/src/magentic_ui/tools/mcp/_aggregate_workbench.py) | Python | 118 | 62 | 35 | 215 |
| [src/magentic\_ui/tools/playwright/\_\_init\_\_.py](/src/magentic_ui/tools/playwright/__init__.py) | Python | 26 | 0 | 2 | 28 |
| [src/magentic\_ui/tools/playwright/browser/\_\_init\_\_.py](/src/magentic_ui/tools/playwright/browser/__init__.py) | Python | 13 | 0 | 2 | 15 |
| [src/magentic\_ui/tools/playwright/browser/base\_playwright\_browser.py](/src/magentic_ui/tools/playwright/browser/base_playwright_browser.py) | Python | 119 | 47 | 29 | 195 |
| [src/magentic\_ui/tools/playwright/browser/headless\_docker\_playwright\_browser.py](/src/magentic_ui/tools/playwright/browser/headless_docker_playwright_browser.py) | Python | 91 | 42 | 23 | 156 |
| [src/magentic\_ui/tools/playwright/browser/local\_playwright\_browser.py](/src/magentic_ui/tools/playwright/browser/local_playwright_browser.py) | Python | 98 | 50 | 22 | 170 |
| [src/magentic\_ui/tools/playwright/browser/utils.py](/src/magentic_ui/tools/playwright/browser/utils.py) | Python | 62 | 20 | 13 | 95 |
| [src/magentic\_ui/tools/playwright/browser/vnc\_docker\_playwright\_browser.py](/src/magentic_ui/tools/playwright/browser/vnc_docker_playwright_browser.py) | Python | 56 | 139 | 14 | 209 |
| [src/magentic\_ui/tools/playwright/page\_script.js](/src/magentic_ui/tools/playwright/page_script.js) | JavaScript JSX | 457 | 93 | 74 | 624 |
| [src/magentic\_ui/tools/playwright/playwright\_controller.py](/src/magentic_ui/tools/playwright/playwright_controller.py) | Python | 741 | 684 | 116 | 1,541 |
| [src/magentic\_ui/tools/playwright/playwright\_state.py](/src/magentic_ui/tools/playwright/playwright_state.py) | Python | 78 | 42 | 21 | 141 |
| [src/magentic\_ui/tools/playwright/types.py](/src/magentic_ui/tools/playwright/types.py) | Python | 83 | 1 | 24 | 108 |
| [src/magentic\_ui/tools/playwright/utils/\_\_init\_\_.py](/src/magentic_ui/tools/playwright/utils/__init__.py) | Python | 0 | 0 | 1 | 1 |
| [src/magentic\_ui/tools/playwright/utils/animation\_utils.py](/src/magentic_ui/tools/playwright/utils/animation_utils.py) | Python | 58 | 120 | 13 | 191 |
| [src/magentic\_ui/tools/playwright/utils/webpage\_text\_utils.py](/src/magentic_ui/tools/playwright/utils/webpage_text_utils.py) | Python | 125 | 67 | 33 | 225 |
| [src/magentic\_ui/tools/tool\_metadata.py](/src/magentic_ui/tools/tool_metadata.py) | Python | 42 | 12 | 17 | 71 |
| [src/magentic\_ui/tools/url\_status\_manager.py](/src/magentic_ui/tools/url_status_manager.py) | Python | 100 | 84 | 20 | 204 |
| [src/magentic\_ui/types.py](/src/magentic_ui/types.py) | Python | 108 | 59 | 31 | 198 |
| [src/magentic\_ui/utils.py](/src/magentic_ui/utils.py) | Python | 135 | 30 | 22 | 187 |
| [src/magentic\_ui/version.py](/src/magentic_ui/version.py) | Python | 3 | 0 | 1 | 4 |

[Summary](results.md) / Details / [Diff Summary](diff.md) / [Diff Details](diff-details.md)