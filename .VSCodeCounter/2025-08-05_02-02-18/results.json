{"file:///Users/<USER>/Documents/note/python/todo/magentic-ui-main/src/magentic_ui/input_func.py": {"language": "Python", "code": 36, "comment": 8, "blank": 19}, "file:///Users/<USER>/Documents/note/python/todo/magentic-ui-main/src/magentic_ui/eval/systems/llm_system.py": {"language": "Python", "code": 53, "comment": 21, "blank": 13}, "file:///Users/<USER>/Documents/note/python/todo/magentic-ui-main/src/magentic_ui/eval/evaluators.py": {"language": "Python", "code": 199, "comment": 65, "blank": 22}, "file:///Users/<USER>/Documents/note/python/todo/magentic-ui-main/src/magentic_ui/eval/systems/example.py": {"language": "Python", "code": 14, "comment": 4, "blank": 5}, "file:///Users/<USER>/Documents/note/python/todo/magentic-ui-main/src/magentic_ui/eval/systems/__init__.py": {"language": "Python", "code": 3, "comment": 0, "blank": 2}, "file:///Users/<USER>/Documents/note/python/todo/magentic-ui-main/src/magentic_ui/eval/benchmarks/assistantbench/evaluate_utils/evaluate_strings.py": {"language": "Python", "code": 123, "comment": 21, "blank": 36}, "file:///Users/<USER>/Documents/note/python/todo/magentic-ui-main/src/magentic_ui/eval/benchmarks/assistantbench/evaluate_utils/utils.py": {"language": "Python", "code": 17, "comment": 4, "blank": 4}, "file:///Users/<USER>/Documents/note/python/todo/magentic-ui-main/src/magentic_ui/eval/benchmarks/assistantbench/evaluate_utils/readme.md": {"language": "<PERSON><PERSON>", "code": 1, "comment": 0, "blank": 1}, "file:///Users/<USER>/Documents/note/python/todo/magentic-ui-main/src/magentic_ui/eval/benchmarks/assistantbench/evaluate_utils/evaluate_dicts.py": {"language": "Python", "code": 58, "comment": 1, "blank": 13}, "file:///Users/<USER>/Documents/note/python/todo/magentic-ui-main/src/magentic_ui/eval/benchmarks/assistantbench/evaluate_utils/evaluate_factory.py": {"language": "Python", "code": 21, "comment": 1, "blank": 9}, "file:///Users/<USER>/Documents/note/python/todo/magentic-ui-main/src/magentic_ui/eval/benchmarks/assistantbench/evaluate_utils/evaluate_numbers.py": {"language": "Python", "code": 28, "comment": 2, "blank": 6}, "file:///Users/<USER>/Documents/note/python/todo/magentic-ui-main/src/magentic_ui/eval/benchmarks/assistantbench/evaluate_utils/__init__.py": {"language": "Python", "code": 0, "comment": 0, "blank": 1}, "file:///Users/<USER>/Documents/note/python/todo/magentic-ui-main/src/magentic_ui/eval/benchmarks/assistantbench/evaluate_utils/assistantbench_evaluator.py": {"language": "Python", "code": 111, "comment": 13, "blank": 21}, "file:///Users/<USER>/Documents/note/python/todo/magentic-ui-main/src/magentic_ui/eval/benchmarks/assistantbench/__init__.py": {"language": "Python", "code": 0, "comment": 0, "blank": 1}, "file:///Users/<USER>/Documents/note/python/todo/magentic-ui-main/src/magentic_ui/eval/benchmarks/assistantbench/README.md": {"language": "<PERSON><PERSON>", "code": 9, "comment": 0, "blank": 1}, "file:///Users/<USER>/Documents/note/python/todo/magentic-ui-main/src/magentic_ui/eval/benchmarks/assistantbench/assistantbench.py": {"language": "Python", "code": 93, "comment": 18, "blank": 14}, "file:///Users/<USER>/Documents/note/python/todo/magentic-ui-main/src/magentic_ui/eval/benchmarks/custom/__init__.py": {"language": "Python", "code": 0, "comment": 0, "blank": 1}, "file:///Users/<USER>/Documents/note/python/todo/magentic-ui-main/src/magentic_ui/eval/benchmarks/custom/README.md": {"language": "<PERSON><PERSON>", "code": 0, "comment": 0, "blank": 1}, "file:///Users/<USER>/Documents/note/python/todo/magentic-ui-main/src/magentic_ui/eval/benchmarks/custom/custom.py": {"language": "Python", "code": 122, "comment": 42, "blank": 17}, "file:///Users/<USER>/Documents/note/python/todo/magentic-ui-main/src/magentic_ui/eval/benchmarks/baseqa.py": {"language": "Python", "code": 13, "comment": 1, "blank": 5}, "file:///Users/<USER>/Documents/note/python/todo/magentic-ui-main/src/magentic_ui/eval/benchmarks/gpqa/__init__.py": {"language": "Python", "code": 0, "comment": 0, "blank": 1}, "file:///Users/<USER>/Documents/note/python/todo/magentic-ui-main/src/magentic_ui/eval/benchmarks/gpqa/README.md": {"language": "<PERSON><PERSON>", "code": 14, "comment": 0, "blank": 3}, "file:///Users/<USER>/Documents/note/python/todo/magentic-ui-main/src/magentic_ui/eval/benchmarks/gpqa/gpqa.py": {"language": "Python", "code": 106, "comment": 7, "blank": 18}, "file:///Users/<USER>/Documents/note/python/todo/magentic-ui-main/src/magentic_ui/eval/benchmarks/README.md": {"language": "<PERSON><PERSON>", "code": 0, "comment": 0, "blank": 1}, "file:///Users/<USER>/Documents/note/python/todo/magentic-ui-main/src/magentic_ui/eval/benchmarks/gaia/__init__.py": {"language": "Python", "code": 0, "comment": 0, "blank": 1}, "file:///Users/<USER>/Documents/note/python/todo/magentic-ui-main/src/magentic_ui/eval/benchmarks/__init__.py": {"language": "Python", "code": 18, "comment": 0, "blank": 2}, "file:///Users/<USER>/Documents/note/python/todo/magentic-ui-main/src/magentic_ui/eval/benchmarks/gaia/README.md": {"language": "<PERSON><PERSON>", "code": 9, "comment": 0, "blank": 1}, "file:///Users/<USER>/Documents/note/python/todo/magentic-ui-main/src/magentic_ui/eval/utils.py": {"language": "Python", "code": 35, "comment": 11, "blank": 12}, "file:///Users/<USER>/Documents/note/python/todo/magentic-ui-main/src/magentic_ui/eval/basesystem.py": {"language": "Python", "code": 37, "comment": 46, "blank": 10}, "file:///Users/<USER>/Documents/note/python/todo/magentic-ui-main/src/magentic_ui/eval/__init__.py": {"language": "Python", "code": 0, "comment": 0, "blank": 1}, "file:///Users/<USER>/Documents/note/python/todo/magentic-ui-main/src/magentic_ui/eval/core.py": {"language": "Python", "code": 454, "comment": 184, "blank": 66}, "file:///Users/<USER>/Documents/note/python/todo/magentic-ui-main/src/magentic_ui/eval/models.py": {"language": "Python", "code": 90, "comment": 10, "blank": 54}, "file:///Users/<USER>/Documents/note/python/todo/magentic-ui-main/src/magentic_ui/eval/README.md": {"language": "<PERSON><PERSON>", "code": 0, "comment": 0, "blank": 1}, "file:///Users/<USER>/Documents/note/python/todo/magentic-ui-main/src/magentic_ui/eval/benchmark.py": {"language": "Python", "code": 116, "comment": 54, "blank": 27}, "file:///Users/<USER>/Documents/note/python/todo/magentic-ui-main/src/magentic_ui/approval_guard.py": {"language": "Python", "code": 251, "comment": 27, "blank": 50}, "file:///Users/<USER>/Documents/note/python/todo/magentic-ui-main/src/magentic_ui/eval/benchmarks/gaia/gaia.py": {"language": "Python", "code": 150, "comment": 21, "blank": 20}, "file:///Users/<USER>/Documents/note/python/todo/magentic-ui-main/src/magentic_ui/task_team.py": {"language": "Python", "code": 221, "comment": 14, "blank": 23}, "file:///Users/<USER>/Documents/note/python/todo/magentic-ui-main/src/magentic_ui/utils.py": {"language": "Python", "code": 135, "comment": 30, "blank": 22}, "file:///Users/<USER>/Documents/note/python/todo/magentic-ui-main/src/magentic_ui/_docker.py": {"language": "Python", "code": 58, "comment": 0, "blank": 20}, "file:///Users/<USER>/Documents/note/python/todo/magentic-ui-main/src/magentic_ui/types.py": {"language": "Python", "code": 108, "comment": 59, "blank": 31}, "file:///Users/<USER>/Documents/note/python/todo/magentic-ui-main/src/magentic_ui/cli/__init__.py": {"language": "Python", "code": 7, "comment": 1, "blank": 3}, "file:///Users/<USER>/Documents/note/python/todo/magentic-ui-main/src/magentic_ui/cli/pretty_console.py": {"language": "Python", "code": 524, "comment": 132, "blank": 149}, "file:///Users/<USER>/Documents/note/python/todo/magentic-ui-main/src/magentic_ui/__init__.py": {"language": "Python", "code": 30, "comment": 0, "blank": 3}, "file:///Users/<USER>/Documents/note/python/todo/magentic-ui-main/src/magentic_ui/eval/benchmarks/bearcubs/__init__.py": {"language": "Python", "code": 0, "comment": 0, "blank": 1}, "file:///Users/<USER>/Documents/note/python/todo/magentic-ui-main/src/magentic_ui/version.py": {"language": "Python", "code": 3, "comment": 0, "blank": 1}, "file:///Users/<USER>/Documents/note/python/todo/magentic-ui-main/src/magentic_ui/_cli.py": {"language": "Python", "code": 738, "comment": 38, "blank": 61}, "file:///Users/<USER>/Documents/note/python/todo/magentic-ui-main/src/magentic_ui/agents/_utils.py": {"language": "Python", "code": 33, "comment": 3, "blank": 8}, "file:///Users/<USER>/Documents/note/python/todo/magentic-ui-main/src/magentic_ui/agents/_user_proxy.py": {"language": "Python", "code": 8, "comment": 0, "blank": 1}, "file:///Users/<USER>/Documents/note/python/todo/magentic-ui-main/src/magentic_ui/agents/_coder.py": {"language": "Python", "code": 477, "comment": 112, "blank": 60}, "file:///Users/<USER>/Documents/note/python/todo/magentic-ui-main/src/magentic_ui/agents/users/__init__.py": {"language": "Python", "code": 3, "comment": 0, "blank": 2}, "file:///Users/<USER>/Documents/note/python/todo/magentic-ui-main/src/magentic_ui/agents/__init__.py": {"language": "Python", "code": 11, "comment": 0, "blank": 2}, "file:///Users/<USER>/Documents/note/python/todo/magentic-ui-main/src/magentic_ui/agents/users/_metadata_user_proxy.py": {"language": "Python", "code": 428, "comment": 37, "blank": 19}, "file:///Users/<USER>/Documents/note/python/todo/magentic-ui-main/src/magentic_ui/agents/users/_dummy_user_proxy.py": {"language": "Python", "code": 71, "comment": 29, "blank": 11}, "file:///Users/<USER>/Documents/note/python/todo/magentic-ui-main/src/magentic_ui/eval/benchmarks/webgames/__init__.py": {"language": "Python", "code": 0, "comment": 0, "blank": 1}, "file:///Users/<USER>/Documents/note/python/todo/magentic-ui-main/src/magentic_ui/eval/benchmarks/bearcubs/bearcubs.py": {"language": "Python", "code": 69, "comment": 24, "blank": 19}, "file:///Users/<USER>/Documents/note/python/todo/magentic-ui-main/src/magentic_ui/eval/benchmarks/simpleqa/__init__.py": {"language": "Python", "code": 0, "comment": 0, "blank": 1}, "file:///Users/<USER>/Documents/note/python/todo/magentic-ui-main/src/magentic_ui/eval/benchmarks/simpleqa/simpleqa.py": {"language": "Python", "code": 145, "comment": 23, "blank": 26}, "file:///Users/<USER>/Documents/note/python/todo/magentic-ui-main/src/magentic_ui/magentic_ui_config.py": {"language": "Python", "code": 64, "comment": 44, "blank": 12}, "file:///Users/<USER>/Documents/note/python/todo/magentic-ui-main/src/magentic_ui/guarded_action.py": {"language": "Python", "code": 154, "comment": 14, "blank": 42}, "file:///Users/<USER>/Documents/note/python/todo/magentic-ui-main/src/magentic_ui/backend/__init__.py": {"language": "Python", "code": 5, "comment": 0, "blank": 2}, "file:///Users/<USER>/Documents/note/python/todo/magentic-ui-main/src/magentic_ui/learning/__init__.py": {"language": "Python", "code": 2, "comment": 0, "blank": 2}, "file:///Users/<USER>/Documents/note/python/todo/magentic-ui-main/src/magentic_ui/eval/benchmarks/webvoyager/webvoyager.py": {"language": "Python", "code": 211, "comment": 46, "blank": 28}, "file:///Users/<USER>/Documents/note/python/todo/magentic-ui-main/src/magentic_ui/agents/file_surfer/_browser_code_helpers.py": {"language": "Python", "code": 109, "comment": 38, "blank": 33}, "file:///Users/<USER>/Documents/note/python/todo/magentic-ui-main/src/magentic_ui/agents/file_surfer/_file_surfer.py": {"language": "Python", "code": 539, "comment": 114, "blank": 87}, "file:///Users/<USER>/Documents/note/python/todo/magentic-ui-main/src/magentic_ui/learning/memory_provider.py": {"language": "Python", "code": 145, "comment": 28, "blank": 35}, "file:///Users/<USER>/Documents/note/python/todo/magentic-ui-main/src/magentic_ui/eval/benchmarks/webvoyager/README.md": {"language": "<PERSON><PERSON>", "code": 28, "comment": 0, "blank": 22}, "file:///Users/<USER>/Documents/note/python/todo/magentic-ui-main/src/magentic_ui/backend/README.md": {"language": "<PERSON><PERSON>", "code": 1, "comment": 0, "blank": 0}, "file:///Users/<USER>/Documents/note/python/todo/magentic-ui-main/src/magentic_ui/agents/web_surfer/_set_of_mark.py": {"language": "Python", "code": 122, "comment": 53, "blank": 35}, "file:///Users/<USER>/Documents/note/python/todo/magentic-ui-main/src/magentic_ui/agents/file_surfer/__init__.py": {"language": "Python", "code": 2, "comment": 0, "blank": 2}, "file:///Users/<USER>/Documents/note/python/todo/magentic-ui-main/src/magentic_ui/learning/learner.py": {"language": "Python", "code": 76, "comment": 59, "blank": 20}, "file:///Users/<USER>/Documents/note/python/todo/magentic-ui-main/src/magentic_ui/tools/__init__.py": {"language": "Python", "code": 24, "comment": 0, "blank": 2}, "file:///Users/<USER>/Documents/note/python/todo/magentic-ui-main/src/magentic_ui/eval/benchmarks/simpleqa/README.md": {"language": "<PERSON><PERSON>", "code": 16, "comment": 0, "blank": 5}, "file:///Users/<USER>/Documents/note/python/todo/magentic-ui-main/src/magentic_ui/teams/roundrobin_orchestrator.py": {"language": "Python", "code": 340, "comment": 43, "blank": 47}, "file:///Users/<USER>/Documents/note/python/todo/magentic-ui-main/src/magentic_ui/agents/web_surfer/_prompts.py": {"language": "Python", "code": 171, "comment": 0, "blank": 10}, "file:///Users/<USER>/Documents/note/python/todo/magentic-ui-main/src/magentic_ui/agents/mcp/__init__.py": {"language": "Python", "code": 2, "comment": 0, "blank": 2}, "file:///Users/<USER>/Documents/note/python/todo/magentic-ui-main/src/magentic_ui/agents/file_surfer/_tool_definitions.py": {"language": "Python", "code": 169, "comment": 0, "blank": 9}, "file:///Users/<USER>/Documents/note/python/todo/magentic-ui-main/src/magentic_ui/agents/web_surfer/__init__.py": {"language": "Python", "code": 7, "comment": 0, "blank": 2}, "file:///Users/<USER>/Documents/note/python/todo/magentic-ui-main/src/magentic_ui/agents/file_surfer/_code_markdown_file_browser.py": {"language": "Python", "code": 263, "comment": 106, "blank": 46}, "file:///Users/<USER>/Documents/note/python/todo/magentic-ui-main/src/magentic_ui/agents/mcp/_agent.py": {"language": "Python", "code": 70, "comment": 13, "blank": 13}, "file:///Users/<USER>/Documents/note/python/todo/magentic-ui-main/src/magentic_ui/teams/__init__.py": {"language": "Python", "code": 3, "comment": 0, "blank": 2}, "file:///Users/<USER>/Documents/note/python/todo/magentic-ui-main/src/magentic_ui/eval/benchmarks/bearcubs/README.md": {"language": "<PERSON><PERSON>", "code": 9, "comment": 0, "blank": 2}, "file:///Users/<USER>/Documents/note/python/todo/magentic-ui-main/src/magentic_ui/agents/mcp/_config.py": {"language": "Python", "code": 8, "comment": 0, "blank": 5}, "file:///Users/<USER>/Documents/note/python/todo/magentic-ui-main/src/magentic_ui/tools/tool_metadata.py": {"language": "Python", "code": 42, "comment": 12, "blank": 17}, "file:///Users/<USER>/Documents/note/python/todo/magentic-ui-main/src/magentic_ui/eval/benchmarks/simpleqa/prompts.py": {"language": "Python", "code": 80, "comment": 1, "blank": 1}, "file:///Users/<USER>/Documents/note/python/todo/magentic-ui-main/src/magentic_ui/eval/benchmarks/webvoyager/__init__.py": {"language": "Python", "code": 0, "comment": 0, "blank": 1}, "file:///Users/<USER>/Documents/note/python/todo/magentic-ui-main/src/magentic_ui/eval/benchmarks/webgames/README.md": {"language": "<PERSON><PERSON>", "code": 9, "comment": 0, "blank": 2}, "file:///Users/<USER>/Documents/note/python/todo/magentic-ui-main/src/magentic_ui/agents/web_surfer/_tool_definitions.py": {"language": "Python", "code": 630, "comment": 11, "blank": 31}, "file:///Users/<USER>/Documents/note/python/todo/magentic-ui-main/src/magentic_ui/eval/benchmarks/webgames/webgames.py": {"language": "Python", "code": 87, "comment": 37, "blank": 18}, "file:///Users/<USER>/Documents/note/python/todo/magentic-ui-main/src/magentic_ui/backend/teammanager/__init__.py": {"language": "Python", "code": 2, "comment": 0, "blank": 2}, "file:///Users/<USER>/Documents/note/python/todo/magentic-ui-main/src/magentic_ui/backend/teammanager/teammanager.py": {"language": "Python", "code": 372, "comment": 46, "blank": 47}, "file:///Users/<USER>/Documents/note/python/todo/magentic-ui-main/src/magentic_ui/teams/orchestrator/_sentinel_prompts.py": {"language": "Python", "code": 44, "comment": 1, "blank": 4}, "file:///Users/<USER>/Documents/note/python/todo/magentic-ui-main/src/magentic_ui/tools/mcp/__init__.py": {"language": "Python", "code": 8, "comment": 0, "blank": 2}, "file:///Users/<USER>/Documents/note/python/todo/magentic-ui-main/src/magentic_ui/agents/web_surfer/_web_surfer.py": {"language": "Python", "code": 1600, "comment": 331, "blank": 181}, "file:///Users/<USER>/Documents/note/python/todo/magentic-ui-main/src/magentic_ui/agents/web_surfer/_cua_web_surfer.py": {"language": "Python", "code": 224, "comment": 17, "blank": 19}, "file:///Users/<USER>/Documents/note/python/todo/magentic-ui-main/src/magentic_ui/agents/web_surfer/_events.py": {"language": "Python", "code": 9, "comment": 0, "blank": 3}, "file:///Users/<USER>/Documents/note/python/todo/magentic-ui-main/src/magentic_ui/teams/orchestrator/_orchestrator.py": {"language": "Python", "code": 1176, "comment": 160, "blank": 128}, "file:///Users/<USER>/Documents/note/python/todo/magentic-ui-main/src/magentic_ui/tools/playwright/playwright_state.py": {"language": "Python", "code": 78, "comment": 42, "blank": 21}, "file:///Users/<USER>/Documents/note/python/todo/magentic-ui-main/src/magentic_ui/teams/orchestrator/__init__.py": {"language": "Python", "code": 2, "comment": 0, "blank": 2}, "file:///Users/<USER>/Documents/note/python/todo/magentic-ui-main/src/magentic_ui/tools/mcp/_aggregate_workbench.py": {"language": "Python", "code": 118, "comment": 62, "blank": 35}, "file:///Users/<USER>/Documents/note/python/todo/magentic-ui-main/src/magentic_ui/tools/playwright/__init__.py": {"language": "Python", "code": 26, "comment": 0, "blank": 2}, "file:///Users/<USER>/Documents/note/python/todo/magentic-ui-main/src/magentic_ui/teams/orchestrator/_prompts.py": {"language": "Python", "code": 979, "comment": 19, "blank": 51}, "file:///Users/<USER>/Documents/note/python/todo/magentic-ui-main/src/magentic_ui/tools/playwright/page_script.js": {"language": "JavaScript JSX", "code": 457, "comment": 93, "blank": 74}, "file:///Users/<USER>/Documents/note/python/todo/magentic-ui-main/src/magentic_ui/backend/utils/utils.py": {"language": "Python", "code": 219, "comment": 72, "blank": 31}, "file:///Users/<USER>/Documents/note/python/todo/magentic-ui-main/src/magentic_ui/teams/orchestrator/_group_chat.py": {"language": "Python", "code": 191, "comment": 26, "blank": 23}, "file:///Users/<USER>/Documents/note/python/todo/magentic-ui-main/src/magentic_ui/teams/orchestrator/orchestrator_config.py": {"language": "Python", "code": 21, "comment": 22, "blank": 4}, "file:///Users/<USER>/Documents/note/python/todo/magentic-ui-main/src/magentic_ui/tools/playwright/playwright_controller.py": {"language": "Python", "code": 741, "comment": 684, "blank": 116}, "file:///Users/<USER>/Documents/note/python/todo/magentic-ui-main/src/magentic_ui/backend/utils/__init__.py": {"language": "Python", "code": 0, "comment": 0, "blank": 1}, "file:///Users/<USER>/Documents/note/python/todo/magentic-ui-main/src/magentic_ui/backend/web/deps.py": {"language": "Python", "code": 93, "comment": 18, "blank": 35}, "file:///Users/<USER>/Documents/note/python/todo/magentic-ui-main/src/magentic_ui/teams/orchestrator/_utils.py": {"language": "Python", "code": 34, "comment": 4, "blank": 5}, "file:///Users/<USER>/Documents/note/python/todo/magentic-ui-main/src/magentic_ui/tools/playwright/types.py": {"language": "Python", "code": 83, "comment": 1, "blank": 24}, "file:///Users/<USER>/Documents/note/python/todo/magentic-ui-main/src/magentic_ui/backend/web/app.py": {"language": "Python", "code": 149, "comment": 25, "blank": 39}, "file:///Users/<USER>/Documents/note/python/todo/magentic-ui-main/src/magentic_ui/backend/web/config.py": {"language": "Python", "code": 11, "comment": 1, "blank": 7}, "file:///Users/<USER>/Documents/note/python/todo/magentic-ui-main/src/magentic_ui/backend/database/schema_manager.py": {"language": "Python", "code": 288, "comment": 230, "blank": 48}, "file:///Users/<USER>/Documents/note/python/todo/magentic-ui-main/src/magentic_ui/backend/web/initialization.py": {"language": "Python", "code": 66, "comment": 23, "blank": 20}, "file:///Users/<USER>/Documents/note/python/todo/magentic-ui-main/src/magentic_ui/tools/playwright/utils/__init__.py": {"language": "Python", "code": 0, "comment": 0, "blank": 1}, "file:///Users/<USER>/Documents/note/python/todo/magentic-ui-main/src/magentic_ui/tools/playwright/utils/webpage_text_utils.py": {"language": "Python", "code": 125, "comment": 67, "blank": 33}, "file:///Users/<USER>/Documents/note/python/todo/magentic-ui-main/src/magentic_ui/tools/playwright/browser/vnc_docker_playwright_browser.py": {"language": "Python", "code": 56, "comment": 139, "blank": 14}, "file:///Users/<USER>/Documents/note/python/todo/magentic-ui-main/src/magentic_ui/tools/playwright/browser/local_playwright_browser.py": {"language": "Python", "code": 98, "comment": 50, "blank": 22}, "file:///Users/<USER>/Documents/note/python/todo/magentic-ui-main/src/magentic_ui/tools/playwright/browser/base_playwright_browser.py": {"language": "Python", "code": 119, "comment": 47, "blank": 29}, "file:///Users/<USER>/Documents/note/python/todo/magentic-ui-main/src/magentic_ui/tools/playwright/browser/utils.py": {"language": "Python", "code": 62, "comment": 20, "blank": 13}, "file:///Users/<USER>/Documents/note/python/todo/magentic-ui-main/src/magentic_ui/tools/playwright/browser/__init__.py": {"language": "Python", "code": 13, "comment": 0, "blank": 2}, "file:///Users/<USER>/Documents/note/python/todo/magentic-ui-main/src/magentic_ui/tools/playwright/utils/animation_utils.py": {"language": "Python", "code": 58, "comment": 120, "blank": 13}, "file:///Users/<USER>/Documents/note/python/todo/magentic-ui-main/src/magentic_ui/tools/playwright/browser/headless_docker_playwright_browser.py": {"language": "Python", "code": 91, "comment": 42, "blank": 23}, "file:///Users/<USER>/Documents/note/python/todo/magentic-ui-main/src/magentic_ui/backend/database/__init__.py": {"language": "Python", "code": 4, "comment": 0, "blank": 2}, "file:///Users/<USER>/Documents/note/python/todo/magentic-ui-main/src/magentic_ui/backend/web/__init__.py": {"language": "Python", "code": 0, "comment": 0, "blank": 1}, "file:///Users/<USER>/Documents/note/python/todo/magentic-ui-main/src/magentic_ui/backend/database/db_manager.py": {"language": "Python", "code": 329, "comment": 77, "blank": 60}, "file:///Users/<USER>/Documents/note/python/todo/magentic-ui-main/src/magentic_ui/tools/url_status_manager.py": {"language": "Python", "code": 100, "comment": 84, "blank": 20}, "file:///Users/<USER>/Documents/note/python/todo/magentic-ui-main/src/magentic_ui/backend/web/routes/settingsroute.py": {"language": "Python", "code": 53, "comment": 6, "blank": 17}, "file:///Users/<USER>/Documents/note/python/todo/magentic-ui-main/src/magentic_ui/backend/web/routes/__init__.py": {"language": "Python", "code": 0, "comment": 0, "blank": 1}, "file:///Users/<USER>/Documents/note/python/todo/magentic-ui-main/src/magentic_ui/tools/bing_search.py": {"language": "Python", "code": 137, "comment": 41, "blank": 21}, "file:///Users/<USER>/Documents/note/python/todo/magentic-ui-main/src/magentic_ui/backend/web/routes/teams.py": {"language": "Python", "code": 25, "comment": 5, "blank": 12}, "file:///Users/<USER>/Documents/note/python/todo/magentic-ui-main/src/magentic_ui/backend/web/routes/plans.py": {"language": "Python", "code": 184, "comment": 18, "blank": 41}, "file:///Users/<USER>/Documents/note/python/todo/magentic-ui-main/src/magentic_ui/backend/web/routes/sessions.py": {"language": "Python", "code": 123, "comment": 20, "blank": 26}, "file:///Users/<USER>/Documents/note/python/todo/magentic-ui-main/src/magentic_ui/backend/web/managers/__init__.py": {"language": "Python", "code": 2, "comment": 0, "blank": 2}, "file:///Users/<USER>/Documents/note/python/todo/magentic-ui-main/src/magentic_ui/backend/web/routes/ws.py": {"language": "Python", "code": 92, "comment": 11, "blank": 16}, "file:///Users/<USER>/Documents/note/python/todo/magentic-ui-main/src/magentic_ui/backend/web/routes/runs.py": {"language": "Python", "code": 115, "comment": 18, "blank": 32}, "file:///Users/<USER>/Documents/note/python/todo/magentic-ui-main/src/magentic_ui/backend/web/managers/connection.py": {"language": "Python", "code": 540, "comment": 143, "blank": 77}, "file:///Users/<USER>/Documents/note/python/todo/magentic-ui-main/src/magentic_ui/backend/cli.py": {"language": "Python", "code": 225, "comment": 75, "blank": 38}, "file:///Users/<USER>/Documents/note/python/todo/magentic-ui-main/src/magentic_ui/backend/web/routes/validation.py": {"language": "Python", "code": 140, "comment": 16, "blank": 27}, "file:///Users/<USER>/Documents/note/python/todo/magentic-ui-main/src/magentic_ui/backend/datamodel/db.py": {"language": "Python", "code": 193, "comment": 4, "blank": 35}, "file:///Users/<USER>/Documents/note/python/todo/magentic-ui-main/src/magentic_ui/backend/datamodel/types.py": {"language": "Python", "code": 73, "comment": 5, "blank": 29}, "file:///Users/<USER>/Documents/note/python/todo/magentic-ui-main/src/magentic_ui/backend/datamodel/__init__.py": {"language": "Python", "code": 47, "comment": 0, "blank": 3}}