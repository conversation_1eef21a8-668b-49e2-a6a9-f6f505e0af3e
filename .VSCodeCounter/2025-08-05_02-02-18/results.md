# Summary

Date : 2025-08-05 02:02:18

Directory /Users/<USER>/Documents/note/python/todo/magentic-ui-main/src/magentic_ui

Total : 142 files,  18633 codes, 4567 comments, 2993 blanks, all 26193 lines

Summary / [Details](details.md) / [Diff Summary](diff.md) / [Diff Details](diff-details.md)

## Languages
| language | files | code | comment | blank | total |
| :--- | ---: | ---: | ---: | ---: | ---: |
| Python | 129 | 18,080 | 4,474 | 2,879 | 25,433 |
| JavaScript JSX | 1 | 457 | 93 | 74 | 624 |
| Markdown | 12 | 96 | 0 | 40 | 136 |

## Directories
| path | files | code | comment | blank | total |
| :--- | ---: | ---: | ---: | ---: | ---: |
| . | 142 | 18,633 | 4,567 | 2,993 | 26,193 |
| . (Files) | 11 | 1,798 | 234 | 284 | 2,316 |
| agents | 22 | 4,956 | 864 | 581 | 6,401 |
| agents (Files) | 4 | 529 | 115 | 71 | 715 |
| agents/file_surfer | 5 | 1,082 | 258 | 177 | 1,517 |
| agents/mcp | 3 | 80 | 13 | 20 | 113 |
| agents/users | 3 | 502 | 66 | 32 | 600 |
| agents/web_surfer | 7 | 2,763 | 412 | 281 | 3,456 |
| backend | 28 | 3,351 | 813 | 651 | 4,815 |
| backend (Files) | 3 | 231 | 75 | 40 | 346 |
| backend/database | 3 | 621 | 307 | 110 | 1,038 |
| backend/datamodel | 3 | 313 | 9 | 67 | 389 |
| backend/teammanager | 2 | 374 | 46 | 49 | 469 |
| backend/utils | 2 | 219 | 72 | 32 | 323 |
| backend/web | 15 | 1,593 | 304 | 353 | 2,250 |
| backend/web (Files) | 5 | 319 | 67 | 102 | 488 |
| backend/web/managers | 2 | 542 | 143 | 79 | 764 |
| backend/web/routes | 8 | 732 | 94 | 172 | 998 |
| cli | 2 | 531 | 133 | 152 | 816 |
| eval | 47 | 2,548 | 657 | 518 | 3,723 |
| eval (Files) | 8 | 931 | 370 | 193 | 1,494 |
| eval/benchmarks | 36 | 1,547 | 262 | 305 | 2,114 |
| eval/benchmarks (Files) | 3 | 31 | 1 | 8 | 40 |
| eval/benchmarks/assistantbench | 11 | 461 | 60 | 107 | 628 |
| eval/benchmarks/assistantbench (Files) | 3 | 102 | 18 | 16 | 136 |
| eval/benchmarks/assistantbench/evaluate_utils | 8 | 359 | 42 | 91 | 492 |
| eval/benchmarks/bearcubs | 3 | 78 | 24 | 22 | 124 |
| eval/benchmarks/custom | 3 | 122 | 42 | 19 | 183 |
| eval/benchmarks/gaia | 3 | 159 | 21 | 22 | 202 |
| eval/benchmarks/gpqa | 3 | 120 | 7 | 22 | 149 |
| eval/benchmarks/simpleqa | 4 | 241 | 24 | 33 | 298 |
| eval/benchmarks/webgames | 3 | 96 | 37 | 21 | 154 |
| eval/benchmarks/webvoyager | 3 | 239 | 46 | 51 | 336 |
| eval/systems | 3 | 70 | 25 | 20 | 115 |
| learning | 3 | 223 | 87 | 57 | 367 |
| teams | 9 | 2,790 | 275 | 266 | 3,331 |
| teams (Files) | 2 | 343 | 43 | 49 | 435 |
| teams/orchestrator | 7 | 2,447 | 232 | 217 | 2,896 |
| tools | 20 | 2,436 | 1,504 | 484 | 4,424 |
| tools (Files) | 4 | 303 | 137 | 60 | 500 |
| tools/mcp | 2 | 126 | 62 | 37 | 225 |
| tools/playwright | 14 | 2,007 | 1,305 | 387 | 3,699 |
| tools/playwright (Files) | 5 | 1,385 | 820 | 237 | 2,442 |
| tools/playwright/browser | 6 | 439 | 298 | 103 | 840 |
| tools/playwright/utils | 3 | 183 | 187 | 47 | 417 |

Summary / [Details](details.md) / [Diff Summary](diff.md) / [Diff Details](diff-details.md)