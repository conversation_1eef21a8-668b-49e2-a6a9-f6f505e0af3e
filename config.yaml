# Magentic-UI 配置文件
# 您可以在这里配置不同的模型客户端

# 默认使用 OpenAI GPT-4o
gpt4o_client: &gpt4o_client
  provider: OpenAIChatCompletionClient
  config:
    model: gpt-4o-2024-08-06
    api_key: null  # 将在UI中设置或通过环境变量设置
    base_url: null
    max_retries: 5

# 为所有代理使用相同的客户端配置
orchestrator_client: *gpt4o_client
coder_client: *gpt4o_client
web_surfer_client: *gpt4o_client
file_surfer_client: *gpt4o_client
action_guard_client: *gpt4o_client
plan_learning_client: *gpt4o_client

# 如果您想使用其他模型，可以取消注释并修改以下配置：

# # Azure OpenAI 配置示例
# azure_client: &azure_client
#   provider: AzureOpenAIChatCompletionClient
#   config:
#     model: gpt-4o
#     api_key: your-azure-api-key
#     azure_endpoint: https://your-resource.openai.azure.com/
#     api_version: 2024-02-01
#     max_retries: 5

# # Ollama 本地模型配置示例
# ollama_client: &ollama_client
#   provider: OllamaChatCompletionClient
#   config:
#     model: llama2
#     base_url: http://localhost:11434
#     max_retries: 5
