{"name": "Magentic-UI", "version": "1.0.0", "private": true, "description": "Magentic-UI", "author": "Microsoft", "keywords": ["gatsby"], "scripts": {"develop": "gatsby clean && gatsby develop", "dev": "npm run develop", "start": "gatsby clean && gatsby develop", "build": "gatsby clean && rm -rf ../src/magentic_ui/backend/web/ui && PREFIX_PATH_VALUE='' gatsby build --prefix-paths && rsync -a --delete public/ ../src/magentic_ui/backend/web/ui/", "serve": "gatsby serve", "clean": "gatsby clean", "typecheck": "tsc --noEmit"}, "dependencies": {"@dagrejs/dagre": "^1.1.4", "@dnd-kit/core": "^6.2.0", "@headlessui/react": "^2.2.0", "@hello-pangea/dnd": "^17.0.0", "@heroicons/react": "^2.0.18", "@mdx-js/react": "^3.1.0", "@monaco-editor/react": "^4.6.0", "@tailwindcss/typography": "^0.5.9", "@xyflow/react": "^12.3.5", "antd": "^5.22.1", "autoprefixer": "^10.4.20", "gatsby": "^5.14.0", "gatsby-plugin-image": "^3.14.0", "gatsby-plugin-manifest": "^5.14.0", "gatsby-plugin-mdx": "^5.14.0", "gatsby-plugin-postcss": "^6.14.0", "gatsby-plugin-sharp": "^5.14.0", "gatsby-plugin-sitemap": "^6.14.0", "gatsby-source-filesystem": "^5.14.0", "gatsby-transformer-sharp": "^5.14.0", "install": "^0.13.0", "js-yaml": "^4.1.0", "lucide-react": "^0.460.0", "postcss": "^8.4.49", "react": "^18.2.0", "react-dom": "^18.2.0", "react-markdown": "^9.0.1", "react-syntax-highlighter": "^15.6.1", "react-vnc": "^3.0.8", "react-window": "^1.8.11", "remark-gfm": "^4.0.0", "tailwindcss": "^3.4.14", "yarn": "^1.22.22", "zod": "^3.25.63", "zustand": "^5.0.1"}, "devDependencies": {"@types/lodash.debounce": "^4.0.9", "@types/node": "^22.9.0", "@types/react": "^18.2.55", "@types/react-dom": "^18.2.19", "@types/react-syntax-highlighter": "^15.5.13", "@types/uuid": "^10.0.0", "typescript": "^5.3.3"}, "resolutions": {"tar-fs": "2.1.2", "path-to-regexp": "0.1.12", "prismjs": "1.30.0", "cookie": "0.7.0", "base-x": "3.0.11"}, "packageManager": "yarn@1.22.22+sha512.a6b2f7906b721bba3d67d4aff083df04dad64c399707841b7acf00f6b133b7ac24255f2652fa22ae3534329dc6180534e98d17432037ff6fd140556e2bb3137e"}